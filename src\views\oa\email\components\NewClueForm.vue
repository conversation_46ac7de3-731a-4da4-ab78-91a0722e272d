<template>
  <div class="new-clue-container">
    <!-- 左侧表单区域 -->
    <div class="form-section">
      <div class="form-header">
        <h2>新增线索</h2>
        <p>从邮件 "{{ email.subject }}" 创建线索</p>
      </div>

      <div class="form-body">
        <div class="form-group">
          <label>线索名称</label>
          <input type="text" v-model="clueData.name" placeholder="请输入线索名称" />
        </div>

        <div class="form-group">
          <label>公司名称</label>
          <input type="text" v-model="clueData.company" placeholder="请输入公司名称" />
        </div>

        <div class="form-group">
          <label>联系人</label>
          <input type="text" v-model="clueData.contact" placeholder="请输入联系人姓名" />
        </div>

        <div class="form-group">
          <label>联系电话</label>
          <input type="text" v-model="clueData.phone" placeholder="请输入联系电话" />
        </div>

        <div class="form-group">
          <label>邮箱</label>
          <input type="email" v-model="clueData.email" placeholder="请输入邮箱地址" />
        </div>

        <div class="form-group">
          <label>线索来源</label>
          <select v-model="clueData.source">
            <option value="email">邮件</option>
            <option value="website">网站</option>
            <option value="phone">电话</option>
            <option value="referral">推荐</option>
            <option value="other">其他</option>
          </select>
        </div>

        <div class="form-group">
          <label>线索状态</label>
          <select v-model="clueData.status">
            <option value="new">新建</option>
            <option value="following">跟进中</option>
            <option value="converted">已转化</option>
            <option value="invalid">无效</option>
          </select>
        </div>

        <div class="form-group">
          <label>备注</label>
          <textarea v-model="clueData.remarks" placeholder="请输入备注信息"></textarea>
        </div>
      </div>

      <div class="form-footer">
        <button class="cancel-btn" @click="cancel">取消</button>
        <button class="save-btn" @click="saveClue">保存</button>
      </div>
    </div>

    <!-- 右侧邮件内容区域 -->
    <div class="email-section">
      <div class="email-header">
        <h3>原邮件内容</h3>
      </div>

      <div class="email-content-container">
        <!-- 邮件基本信息 -->
        <div class="email-meta-info">
          <div class="email-title">{{ email.subject }}</div>

          <div class="email-meta">
            <div class="sender-info">
              <div class="detail-label">发件人：</div>
              <div class="detail-value">
                <span class="sender-name">{{ email.sender }}</span>
                <span v-if="email.tag" class="sender-tag">@{{ email.tag }}</span>
              </div>
            </div>
            <div class="email-date">{{ email.fullDate || email.time }}</div>
          </div>

          <div class="email-meta" v-if="email.recipients && email.recipients.length">
            <div class="sender-info">
              <div class="detail-label">收件人：</div>
              <div class="detail-value">
                <span v-for="(recipient, idx) in email.recipients" :key="idx" class="recipient">
                  {{ recipient }}
                </span>
              </div>
            </div>
          </div>

          <!-- 邮件标签 -->
          <div class="email-tags" v-if="email.tags && email.tags.length > 0">
            <span
              v-for="(tagId, tagIndex) in email.tags"
              :key="tagIndex"
              class="email-tag"
              :style="{ backgroundColor: getTagColor(tagId) }"
            >
              {{ getTagName(tagId) }}
            </span>
          </div>
        </div>

        <!-- 附件信息 -->
        <div class="attachments-section" v-if="email.attachments && email.attachments.length > 0">
          <div class="attachments-header">
            <paperclip-icon class="icon-small" />
            附件 ({{ email.attachments.length }})
          </div>
          <div class="attachments-list">
            <div
              v-for="(attachment, index) in email.attachments"
              :key="index"
              class="attachment-item"
            >
              <div class="attachment-info">
                <file-text-icon v-if="isTextFile(attachment.name)" class="file-icon" />
                <image-icon v-else-if="isImageFile(attachment.name)" class="file-icon" />
                <file-icon v-else class="file-icon" />
                <div class="attachment-details">
                  <div class="attachment-name">{{ attachment.name }}</div>
                  <div class="attachment-size">{{ formatFileSize(attachment.size) }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 邮件正文内容 -->
        <div class="email-body-section">
          <div class="email-body" v-html="email.body"></div>

          <!-- 邮件签名 -->
          <div class="email-signature" v-if="email.signature">
            <div class="signature-header">签名</div>
            <div class="signature-content" v-html="email.signature"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  PaperclipIcon,
  FileTextIcon,
  ImageIcon,
  FileIcon
} from 'lucide-vue'

export default {
  name: 'NewClueForm',
  components: {
    PaperclipIcon,
    FileTextIcon,
    ImageIcon,
    FileIcon
  },
  props: {
    email: {
      type: Object,
      required: true
    },
    tags: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      clueData: {
        name: '',
        company: '',
        contact: '',
        phone: '',
        email: '',
        source: 'email',
        status: 'new',
        remarks: '',
        relatedEmailId: null
      }
    }
  },
  created() {
    // 预填充表单数据
    this.prefillFormData();
  },
  methods: {
    prefillFormData() {
      if (this.email) {
        // 从邮件中提取公司名称和联系人信息
        this.clueData.name = this.email.subject || '';

        if (this.email.tag) {
          this.clueData.company = this.email.tag;
        }

        this.clueData.contact = this.email.sender || '';
        this.clueData.email = this.email.sender ? `${this.email.sender.toLowerCase().replace(/\s+/g, '.')}@example.com` : '';
        this.clueData.relatedEmailId = this.email.id;

        // 提取邮件内容作为备注
        const emailContent = this.stripHtml(this.email.body || '');
        this.clueData.remarks = `来自邮件: ${this.email.subject}\n\n${emailContent.substring(0, 200)}${emailContent.length > 200 ? '...' : ''}`;
      }
    },

    stripHtml(html) {
      const tmp = document.createElement('div');
      tmp.innerHTML = html;
      return tmp.textContent || tmp.innerText || '';
    },

    saveClue() {
      // 模拟保存线索
      console.log('保存线索:', this.clueData);

      // 显示成功提示
      this.$emit('save-success', {
        ...this.clueData,
        id: Date.now()
      });
    },

    cancel() {
      this.$emit('cancel');
    },

    // 邮件内容显示相关方法
    getTagColor(tagId) {
      const tag = this.tags.find(t => t.id === tagId);
      return tag ? tag.color : '#f0f0f0';
    },

    getTagName(tagId) {
      const tag = this.tags.find(t => t.id === tagId);
      return tag ? tag.name : '未知标签';
    },

    isTextFile(filename) {
      const textExtensions = ['.txt', '.doc', '.docx', '.pdf', '.rtf'];
      return textExtensions.some(ext => filename.toLowerCase().endsWith(ext));
    },

    isImageFile(filename) {
      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg'];
      return imageExtensions.some(ext => filename.toLowerCase().endsWith(ext));
    },

    formatFileSize(bytes) {
      if (!bytes) return '0 B';
      const k = 1024;
      const sizes = ['B', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
  }
}
</script>

<style scoped>
.new-clue-container {
  display: flex;
  height: 100%;
  background-color: #fff;
  gap: 1px;
}

.form-section {
  flex: 0 0 45%;
  padding: 20px;
  background-color: #fff;
  overflow-y: auto;
  border-right: 1px solid #e6e9ed;
}

.email-section {
  flex: 1;
  padding: 20px;
  background-color: #fff;
  overflow-y: auto;
}

.form-header {
  margin-bottom: 24px;
  border-bottom: 1px solid #e6e9ed;
  padding-bottom: 16px;
}

.form-header h2 {
  font-size: 20px;
  margin-bottom: 8px;
  color: #333;
}

.form-header p {
  color: #666;
  font-size: 14px;
}

.form-body {
  margin-bottom: 24px;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 14px;
}

.form-group textarea {
  min-height: 100px;
  resize: vertical;
}

.related-email {
  padding: 12px;
  background-color: #f5f7fa;
  border: 1px solid #e6e9ed;
  border-radius: 4px;
}

.email-subject {
  font-weight: 500;
  margin-bottom: 8px;
}

.email-sender,
.email-time {
  font-size: 13px;
  color: #666;
  margin-bottom: 4px;
}

.form-footer {
  display: flex;
  justify-content: flex-end;
  padding-top: 16px;
  border-top: 1px solid #e6e9ed;
}

.cancel-btn,
.save-btn {
  padding: 10px 20px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
}

.cancel-btn {
  background-color: #f5f7fa;
  border: 1px solid #dcdfe6;
  color: #606266;
  margin-right: 12px;
}

.save-btn {
  background-color: #409EFF;
  border: 1px solid #409EFF;
  color: white;
}

.cancel-btn:hover {
  background-color: #e6e9ed;
}

.save-btn:hover {
  background-color: #66b1ff;
}

/* 邮件内容区域样式 */
.email-header {
  margin-bottom: 20px;
  border-bottom: 1px solid #e6e9ed;
  padding-bottom: 16px;
}

.email-header h3 {
  font-size: 18px;
  color: #333;
  margin: 0;
}

.email-content-container {
  height: calc(100% - 60px);
  overflow-y: auto;
}

.email-meta-info {
  margin-bottom: 20px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e6e9ed;
}

.email-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  line-height: 1.4;
}

.email-meta {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.sender-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.detail-label {
  font-size: 13px;
  color: #666;
  margin-right: 8px;
  min-width: 60px;
}

.detail-value {
  font-size: 14px;
  color: #333;
}

.sender-name {
  font-weight: 500;
}

.sender-tag {
  color: #666;
  font-size: 13px;
}

.recipient {
  margin-right: 8px;
}

.email-date {
  font-size: 13px;
  color: #666;
  white-space: nowrap;
}

.email-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-top: 8px;
}

.email-tag {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  color: white;
  font-weight: 500;
}

/* 附件样式 */
.attachments-section {
  margin-bottom: 20px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e6e9ed;
}

.attachments-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-weight: 500;
  color: #333;
}

.attachments-header .icon-small {
  width: 16px;
  height: 16px;
  margin-right: 8px;
  color: #666;
}

.attachments-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.attachment-item {
  display: flex;
  align-items: center;
  padding: 8px;
  background-color: white;
  border-radius: 4px;
  border: 1px solid #e6e9ed;
}

.attachment-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.file-icon {
  width: 20px;
  height: 20px;
  margin-right: 10px;
  color: #666;
}

.attachment-details {
  flex: 1;
}

.attachment-name {
  font-size: 14px;
  color: #333;
  margin-bottom: 2px;
}

.attachment-size {
  font-size: 12px;
  color: #666;
}

/* 邮件正文样式 */
.email-body-section {
  padding: 16px;
  background-color: white;
  border-radius: 6px;
  border: 1px solid #e6e9ed;
}

.email-body {
  font-size: 14px;
  line-height: 1.6;
  color: #333;
  word-wrap: break-word;
  margin-bottom: 16px;
}

.email-body img {
  max-width: 100%;
  height: auto;
}

.email-signature {
  border-top: 1px solid #e6e9ed;
  padding-top: 16px;
  margin-top: 16px;
}

.signature-header {
  font-size: 13px;
  color: #666;
  margin-bottom: 8px;
  font-weight: 500;
}

.signature-content {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
}
</style>
